name: Local Test Workflow

on:
  push:
    branches: [ local-test ]
  workflow_dispatch:

env:
  DOTNET_VERSION: '8.0.x'
  NODE_VERSION: '20.x'

jobs:
  quick-test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ env.DOTNET_VERSION }}
        cache: true
        cache-dependency-path: |
          src/HarmoniHSE360.Application/packages.lock.json
          src/HarmoniHSE360.Domain/packages.lock.json
          src/HarmoniHSE360.Infrastructure/packages.lock.json
          src/HarmoniHSE360.Web/packages.lock.json

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: src/HarmoniHSE360.Web/ClientApp/package-lock.json

    - name: Restore .NET dependencies
      run: dotnet restore

    - name: Install Node.js dependencies
      working-directory: src/HarmoniHSE360.Web/ClientApp
      run: npm ci

    - name: <PERSON> Prettier check
      working-directory: src/HarmoniHSE360.Web/ClientApp
      run: npm run format:check

    - name: Build .NET application
      run: dotnet build --no-restore --configuration Release

    - name: Build React application
      working-directory: src/HarmoniHSE360.Web/ClientApp
      run: npm run build