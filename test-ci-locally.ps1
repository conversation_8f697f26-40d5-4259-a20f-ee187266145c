#!/usr/bin/env pwsh
# Script to test CI/CD pipeline locally before pushing

Write-Host "Testing CI/CD Pipeline Locally" -ForegroundColor Green
Write-Host "==============================" -ForegroundColor Green

# Test 1: .NET Restore
Write-Host "`nTest 1: .NET Restore" -ForegroundColor Yellow
dotnet restore
if ($LASTEXITCODE -ne 0) {
    Write-Host "ERROR: .NET restore failed" -ForegroundColor Red
    exit 1
}
Write-Host "✓ .NET restore successful" -ForegroundColor Green

# Test 2: .NET Build
Write-Host "`nTest 2: .NET Build" -ForegroundColor Yellow
dotnet build --no-restore --configuration Release
if ($LASTEXITCODE -ne 0) {
    Write-Host "ERROR: .NET build failed" -ForegroundColor Red
    exit 1
}
Write-Host "✓ .NET build successful" -ForegroundColor Green

# Test 3: NPM Install
Write-Host "`nTest 3: NPM CI" -ForegroundColor Yellow
Push-Location src/HarmoniHSE360.Web/ClientApp
npm ci
if ($LASTEXITCODE -ne 0) {
    Write-Host "ERROR: npm ci failed" -ForegroundColor Red
    Pop-Location
    exit 1
}
Write-Host "✓ npm ci successful" -ForegroundColor Green

# Test 4: Prettier Check
Write-Host "`nTest 4: Prettier Check" -ForegroundColor Yellow
npm run format:check
if ($LASTEXITCODE -ne 0) {
    Write-Host "ERROR: Prettier check failed" -ForegroundColor Red
    Pop-Location
    exit 1
}
Write-Host "✓ Prettier check successful" -ForegroundColor Green

# Test 5: ESLint
Write-Host "`nTest 5: ESLint" -ForegroundColor Yellow
npm run lint
if ($LASTEXITCODE -ne 0) {
    Write-Host "ERROR: ESLint failed" -ForegroundColor Red
    Pop-Location
    exit 1
}
Write-Host "✓ ESLint successful" -ForegroundColor Green

# Test 6: React Build
Write-Host "`nTest 6: React Build" -ForegroundColor Yellow
npm run build
if ($LASTEXITCODE -ne 0) {
    Write-Host "ERROR: React build failed" -ForegroundColor Red
    Pop-Location
    exit 1
}
Write-Host "✓ React build successful" -ForegroundColor Green

Pop-Location

# Test 7: Docker Build
Write-Host "`nTest 7: Docker Build (if Docker is available)" -ForegroundColor Yellow
if (Get-Command docker -ErrorAction SilentlyContinue) {
    docker build -f Dockerfile.flyio -t harmonihse360-test:local .
    if ($LASTEXITCODE -ne 0) {
        Write-Host "ERROR: Docker build failed" -ForegroundColor Red
        exit 1
    }
    Write-Host "✓ Docker build successful" -ForegroundColor Green
} else {
    Write-Host "- Docker not available, skipping Docker build test" -ForegroundColor Gray
}

Write-Host "`n==============================" -ForegroundColor Green
Write-Host "All CI/CD tests passed locally!" -ForegroundColor Green
Write-Host "You can now safely push your changes." -ForegroundColor Green