// Use Sass modules instead of @import
@use 'sass:color';

// Harmoni HSE 360 Branding Colors
$teal-primary: #0097A7;
$deep-blue: #004D6E;
$leaf-green: #66BB6A;
$accent-yellow: #F9A825;
$soft-grey: #F5F5F5;
$charcoal: #212121;

// Import CoreUI with configuration
@use '@coreui/coreui/scss/coreui' with (
  $primary: $teal-primary,
  $secondary: $deep-blue,
  $success: $leaf-green,
  $warning: $accent-yellow,
  $body-bg: $soft-grey,
  $body-color: $charcoal
);

// Custom styles following Harmoni branding
:root {
  --harmoni-teal: #{$teal-primary};
  --harmoni-blue: #{$deep-blue};
  --harmoni-green: #{$leaf-green};
  --harmoni-yellow: #{$accent-yellow};
  --harmoni-grey: #{$soft-grey};
  --harmoni-charcoal: #{$charcoal};
}

// Typography
body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-weight: 600;
}

// Buttons - Harmoni style
.btn {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;

  &.btn-primary {
    background-color: var(--harmoni-teal);
    border-color: var(--harmoni-teal);

    &:hover, &:focus {
      background-color: color.adjust($teal-primary, $lightness: -10%);
      border-color: color.adjust($teal-primary, $lightness: -10%);
    }
  }

  &.btn-secondary {
    background-color: transparent;
    color: var(--harmoni-teal);
    border: 1px solid var(--harmoni-teal);

    &:hover, &:focus {
      background-color: var(--harmoni-teal);
      color: white;
    }
  }

  &.btn-danger {
    background-color: var(--harmoni-yellow);
    border-color: var(--harmoni-yellow);
    color: var(--harmoni-charcoal);
  }
}

// Cards - Harmoni style
.card {
  background: #FFFFFF;
  box-shadow: 0 2px 6px rgba(0,0,0,0.1);
  border: none;
  border-radius: 8px;
  padding: 16px;

  .card-header {
    background-color: transparent;
    border-bottom: 2px solid var(--harmoni-teal);
    font-weight: 600;
  }
}

// Forms - Harmoni style
.form-control, .form-select {
  border: 1px solid #CCCCCC;
  border-radius: 8px;

  &:focus {
    border-color: var(--harmoni-teal);
    box-shadow: 0 0 0 0.2rem rgba(0, 151, 167, 0.25);
  }
}

.form-label {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

// Azure Portal-style Sidebar with Harmoni HSE 360 colors
.sidebar {
  background-color: #2d2d30;
  border-right: none;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease-in-out;
  width: 240px;

  // Always show full brand, remove minimized version
  .sidebar-brand-full {
    display: block;
  }
  
  .sidebar-brand-minimized {
    display: none;
  }

  // Brand/Logo area
  .sidebar-brand {
    background-color: #2d2d30;
    border-bottom: 1px solid #3a3a3c;
    padding: 16px;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .sidebar-logo {
      max-width: 140px;
      height: auto;
      object-fit: contain;
    }
    
    .sidebar-logo-minimized {
      width: 24px;
      height: 24px;
      object-fit: contain;
      display: none;
    }
  }

  // Navigation styling
  .sidebar-nav {
    padding: 8px 0;
  }

  .nav-link {
    color: #e3e3e3;
    text-decoration: none;
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-radius: 0;
    transition: all 0.2s ease-in-out;
    position: relative;
    border-left: 3px solid transparent;
    font-size: 14px;
    font-weight: 400;
    margin: 2px 0;

    &:hover {
      color: #ffffff;
      background-color: #3a3a3c;
      text-decoration: none;
    }

    &.active {
      color: #ffffff;
      background-color: #3a3a3c;
      border-left-color: var(--harmoni-teal);
      font-weight: 500;
    }
  }

  .nav-icon {
    color: #e3e3e3;
    margin-right: 12px;
    flex-shrink: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    
    .active & {
      color: var(--harmoni-teal);
    }
  }

  // CoreUI nav item styling overrides
  .nav-item {
    .nav-link {
      color: #e3e3e3 !important;
      
      &:hover {
        color: #ffffff !important;
      }
      
      &.active {
        color: #ffffff !important;
      }
    }
  }

  // Navigation groups (dropdown sections)
  .nav-group {
    .nav-group-toggler {
      color: #e3e3e3;
      background-color: transparent;
      border: none;
      padding: 12px 16px;
      width: 100%;
      text-align: left;
      display: flex;
      align-items: center;
      transition: all 0.2s ease-in-out;
      border-left: 3px solid transparent;
      margin: 2px 0;

      &:hover {
        color: #ffffff;
        background-color: #3a3a3c;
      }

      &[aria-expanded="true"] {
        color: #ffffff;
        background-color: #3a3a3c;
        // Remove automatic border highlight when expanded
        // Only show border when no child is active
      }

      // Only highlight parent when it's specifically selected (not when child is active)
      &.active:not([aria-expanded="true"]) {
        border-left-color: var(--harmoni-teal);
      }

      .nav-icon {
        margin-right: 12px;
      }
    }

    .nav-group-items {
      background-color: #252526;
      border-left: 3px solid #3a3a3c;
      margin-left: 0;

      .nav-item {
        .nav-link {
          padding-left: 44px;
          font-size: 13px;
          color: #cccccc !important;
          border-left: 3px solid transparent;
          margin: 1px 0;
          transition: all 0.2s ease-in-out;

          &:hover {
            color: #ffffff !important;
            background-color: #3a3a3c;
          }

          &.active {
            color: #ffffff !important;
            background-color: #3a3a3c;
            border-left-color: var(--harmoni-teal);
            font-weight: 500;
          }
        }
      }
    }
  }

  // Section titles (remove or minimize)
  .nav-title {
    color: #8a8a8a;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 16px 16px 8px 16px;
    margin: 0;
  }

  // Toggler button styling
  .sidebar-toggler {
    background-color: #3a3a3c;
    border: none;
    color: #e3e3e3;
    padding: 12px;
    transition: all 0.2s ease-in-out;

    &:hover {
      background-color: #4a4a4c;
      color: #ffffff;
    }

    &:focus {
      box-shadow: 0 0 0 2px var(--harmoni-teal);
    }
  }
}

// Wrapper layout adjustments for sidebar visibility
.wrapper {
  transition: margin-left 0.3s ease-in-out;
  
  &.sidebar-visible {
    margin-left: 240px; // Width of sidebar when visible
    
    @media (max-width: 991.98px) {
      margin-left: 0; // No margin on mobile, sidebar overlays
    }
  }
  
  &.sidebar-hidden {
    margin-left: 0; // No margin when sidebar is hidden
  }
}

// Header customization
.header {
  background-color: white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

// Dashboard widgets
.dashboard-widget {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  }

  .widget-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;

    &.primary {
      background-color: rgba(0, 151, 167, 0.1);
      color: var(--harmoni-teal);
    }

    &.success {
      background-color: rgba(102, 187, 106, 0.1);
      color: var(--harmoni-green);
    }

    &.warning {
      background-color: rgba(249, 168, 37, 0.1);
      color: var(--harmoni-yellow);
    }
  }
}

// Tables
.table {
  th {
    background-color: var(--harmoni-grey);
    font-weight: 600;
    text-transform: uppercase;
    font-size: 12px;
    letter-spacing: 0.5px;
  }

  tbody tr:hover {
    background-color: rgba(0, 151, 167, 0.05);
  }
}

// Alerts
.alert {
  border-radius: 8px;
  border: none;

  &.alert-info {
    background-color: rgba(0, 151, 167, 0.1);
    color: color.adjust($teal-primary, $lightness: -20%);
  }

  &.alert-success {
    background-color: rgba(102, 187, 106, 0.1);
    color: color.adjust($leaf-green, $lightness: -20%);
  }

  &.alert-warning {
    background-color: rgba(249, 168, 37, 0.1);
    color: color.adjust($accent-yellow, $lightness: -20%);
  }
}

// Loading states
.loading-spinner {
  color: var(--harmoni-teal);
}

// Mobile responsiveness
@media (max-width: 768px) {
  .card {
    padding: 12px;
  }

  .btn {
    min-height: 44px; // Touch target size
  }

  .dashboard-widget {
    padding: 16px;
  }

  // Audit trail mobile responsiveness
  .list-group {
    overflow-x: auto;
    white-space: nowrap;
    
    .list-group-item {
      min-width: 300px;
      white-space: normal;
    }
  }

  // Activity history mobile styling
  .audit-trail-mobile {
    .list-group-item {
      padding: 12px;
      
      .d-flex {
        flex-direction: column;
        align-items: flex-start !important;
        
        .me-3 {
          margin-right: 0 !important;
          margin-bottom: 8px;
        }
        
        .justify-content-between {
          width: 100%;
          flex-direction: column;
          align-items: flex-start !important;
          
          small {
            margin-top: 4px;
            align-self: flex-start;
          }
        }
      }
    }
  }

  // Related Information mobile styling
  .related-info-mobile {
    .list-group-item {
      padding: 16px;
      border-radius: 8px !important;
      margin-bottom: 8px;
      background-color: #f8f9fa;
      border: 2px solid #dee2e6 !important;
      
      &:hover {
        background-color: rgba(0, 151, 167, 0.1) !important;
        border-color: var(--harmoni-teal) !important;
        transform: scale(1.02);
        
        span {
          color: var(--harmoni-teal) !important;
          font-weight: 600 !important;
        }
        
        small {
          color: var(--harmoni-blue) !important;
        }
        
        .badge {
          background-color: var(--harmoni-teal) !important;
          transform: scale(1.1);
        }
      }
      
      &:active {
        transform: scale(0.98);
      }
      
      // Add touch feedback
      &:focus {
        outline: 3px solid var(--harmoni-teal);
        outline-offset: 2px;
      }
      
      // Make text and badges more prominent
      span {
        font-size: 16px;
        font-weight: 500;
      }
      
      small {
        font-size: 14px;
        margin-top: 2px;
      }
      
      .badge {
        font-size: 14px;
        padding: 6px 10px;
        border-radius: 6px;
        font-weight: 600;
      }
      
      // Add visual indicator
      &::after {
        content: '\203A';
        position: absolute;
        right: 16px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 20px;
        color: #6c757d;
        transition: all 0.2s ease-in-out;
      }
      
      &:hover::after {
        color: var(--harmoni-teal);
        transform: translateY(-50%) translateX(3px);
      }
    }
  }
}

// Accessibility
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

// Focus states
*:focus {
  outline: 2px solid var(--harmoni-teal);
  outline-offset: 2px;
}

// Clickable elements styling
.cursor-pointer {
  cursor: pointer !important;
  transition: all 0.15s ease-in-out;

  &:hover {
    background-color: rgba(0, 151, 167, 0.05);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  &:active {
    transform: translateY(0);
  }
}

// Related Information section styling
.list-group-item.cursor-pointer {
  border: 1px solid #dee2e6;
  margin-bottom: 2px;
  border-radius: 6px;

  &:hover {
    background-color: rgba(0, 151, 167, 0.08);
    border-color: var(--harmoni-teal);
    
    span {
      color: var(--harmoni-teal);
      font-weight: 500;
    }
    
    .badge {
      background-color: var(--harmoni-teal) !important;
    }
  }

  &:focus {
    outline: 2px solid var(--harmoni-teal);
    outline-offset: 2px;
  }
}

// Dropdown menu items cursor styling
.dropdown-item {
  cursor: pointer !important;
  transition: all 0.15s ease-in-out;

  &:hover {
    cursor: pointer !important;
  }

  &:disabled {
    cursor: not-allowed !important;
  }
}

// Project Settings component styling
.project-settings {
  position: sticky;
  bottom: 0;
  background-color: #2d2d30;
  border-top: 1px solid #3a3a3c;
  padding: 8px;
  
  .project-settings-toggle {
    border: none !important;
    background-color: transparent !important;
    color: #e3e3e3 !important;
    padding: 12px 16px;
    border-radius: 0 !important;
    font-size: 14px;
    
    // Remove CoreUI's default dropdown arrow
    &::after {
      display: none !important;
    }
    
    &:hover {
      background-color: #3a3a3c !important;
      color: #ffffff !important;
    }
    
    &:focus {
      box-shadow: 0 0 0 2px var(--harmoni-teal) !important;
    }
    
    .transition-transform {
      transition: transform 0.2s ease-in-out;
    }
    
    .rotate-180 {
      transform: rotate(180deg);
    }
  }
  
  .project-settings-menu {
    background-color: #2d2d30;
    border: 1px solid #3a3a3c;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    min-width: 280px;
    
    .dropdown-header {
      background-color: #252526;
      color: #ffffff;
      font-size: 11px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      padding: 8px 16px;
    }
    
    .dropdown-divider {
      border-color: #3a3a3c;
      margin: 4px 0;
    }
    
    .project-settings-item {
      color: #e3e3e3;
      padding: 12px 16px;
      border: none;
      background-color: transparent;
      
      &:hover {
        background-color: #3a3a3c;
        color: #ffffff;
      }
      
      &:focus {
        background-color: #3a3a3c;
        color: #ffffff;
        outline: 2px solid var(--harmoni-teal);
        outline-offset: -2px;
      }
      
      .fw-semibold {
        font-weight: 500;
        margin-bottom: 2px;
      }
      
      small {
        font-size: 12px;
        line-height: 1.3;
      }
    }
  }
}

// Tablet responsiveness
@media (max-width: 991.98px) and (min-width: 769px) {
  .related-info-mobile {
    .list-group-item {
      padding: 14px;
      
      span {
        font-size: 15px;
      }
      
      .badge {
        font-size: 13px;
        padding: 5px 8px;
      }
    }
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .btn-primary {
    border: 2px solid white;
  }

  .card {
    border: 1px solid var(--harmoni-charcoal);
  }
}