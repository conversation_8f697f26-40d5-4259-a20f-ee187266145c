#!/bin/bash
# Script to test CI/CD pipeline locally before pushing

GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}Testing CI/CD Pipeline Locally${NC}"
echo -e "${GREEN}==============================${NC}"

# Test 1: .NET Restore
echo -e "\n${YELLOW}Test 1: .NET Restore${NC}"
if dotnet restore; then
    echo -e "${GREEN}✓ .NET restore successful${NC}"
else
    echo -e "${RED}ERROR: .NET restore failed${NC}"
    exit 1
fi

# Test 2: .NET Build
echo -e "\n${YELLOW}Test 2: .NET Build${NC}"
if dotnet build --no-restore --configuration Release; then
    echo -e "${GREEN}✓ .NET build successful${NC}"
else
    echo -e "${RED}ERROR: .NET build failed${NC}"
    exit 1
fi

# Test 3: NPM Install
echo -e "\n${YELLOW}Test 3: NPM CI${NC}"
cd src/HarmoniHSE360.Web/ClientApp
if npm ci; then
    echo -e "${GREEN}✓ npm ci successful${NC}"
else
    echo -e "${RED}ERROR: npm ci failed${NC}"
    exit 1
fi

# Test 4: Prettier Check
echo -e "\n${YELLOW}Test 4: Prettier Check${NC}"
if npm run format:check; then
    echo -e "${GREEN}✓ Prettier check successful${NC}"
else
    echo -e "${RED}ERROR: Prettier check failed${NC}"
    exit 1
fi

# Test 5: ESLint
echo -e "\n${YELLOW}Test 5: ESLint${NC}"
if npm run lint; then
    echo -e "${GREEN}✓ ESLint successful${NC}"
else
    echo -e "${RED}ERROR: ESLint failed${NC}"
    exit 1
fi

# Test 6: React Build
echo -e "\n${YELLOW}Test 6: React Build${NC}"
if npm run build; then
    echo -e "${GREEN}✓ React build successful${NC}"
else
    echo -e "${RED}ERROR: React build failed${NC}"
    exit 1
fi

cd ../../..

# Test 7: Docker Build
echo -e "\n${YELLOW}Test 7: Docker Build (if Docker is available)${NC}"
if command -v docker &> /dev/null; then
    if docker build -f Dockerfile.flyio -t harmonihse360-test:local .; then
        echo -e "${GREEN}✓ Docker build successful${NC}"
    else
        echo -e "${RED}ERROR: Docker build failed${NC}"
        exit 1
    fi
else
    echo -e "- Docker not available, skipping Docker build test"
fi

echo -e "\n${GREEN}==============================${NC}"
echo -e "${GREEN}All CI/CD tests passed locally!${NC}"
echo -e "${GREEN}You can now safely push your changes.${NC}"